import os
import time
from datetime import datetime
from pathlib import Path

# Early proxy setup before any other imports
from dotenv import load_dotenv
load_dotenv()

# Early proxy setup - must be executed before other library imports
nordvpn_proxy = os.getenv('NORDVPN_PROXY')
if nordvpn_proxy:
    from .proxy_manager import setup_early_proxy
    # Note: Still using original SOCKS5 proxy for early setup
    # Will be converted to HTTP proxy later through ProxyManager
    setup_early_proxy(nordvpn_proxy)

from marker.converters.pdf import PdfConverter
from marker.models import create_model_dict
from marker.config.parser import ConfigParser
from marker.output import text_from_rendered
from .proxy_manager import ProxyManager, ensure_proxy_persistence

# Create global proxy manager
proxy_manager = ProxyManager()

def create_gemini_config_with_http_proxy():
    """Create Gemini model configuration using HTTP proxy converted from SOCKS5"""
    print(f"\n{'='*50}")
    print("Configuring Gemini model with HTTP proxy (converted from SOCKS5)")
    print(f"{'='*50}")
    
    # First create configuration dictionary with timeout settings
    config = {
        "use_llm": os.getenv('USE_LLM', 'true').lower() == 'true',
        "llm_service": os.getenv('MARKER_GEMINI_SERIVES'),
        "gemini_api_key": os.getenv('GEMINI_API_KEY'),
        "gemini_model_name": os.getenv('GEMINI_MODEL'),
        "format_lines": os.getenv('MARKER_FORMAT_LINES', 'true').lower() == 'true',
        "output_format": os.getenv('MARKER_OUTPUT_FORMAT', 'markdown'),
        "force_ocr": os.getenv('MARKER_FORCE_OCR', 'false').lower() == 'true',
        "batch_multiplier": int(os.getenv('MARKER_BATCH_MULTIPLIER', '1')),
        "langs": os.getenv('MARKER_LANG', 'en').split(','),
        # Add timeout settings
        "api_timeout": int(os.getenv('GEMINI_API_TIMEOUT', '600')),  # 10 minutes timeout
        "connection_timeout": int(os.getenv('CONNECTION_TIMEOUT', '120')),  # 2 minutes connection timeout
        "proxy_timeout": int(os.getenv('PROXY_TIMEOUT', '60')),  # 1 minute proxy timeout
        "max_retries": int(os.getenv('MAX_RETRIES', '5'))  # Maximum retry count
    }
    
    nordvpn_proxy = os.getenv('NORDVPN_PROXY')
    if nordvpn_proxy:
        # Use enhanced proxy setup that automatically converts SOCKS5 to HTTP
        proxy_manager.set_global_proxy_with_conversion(nordvpn_proxy, force=True)
        
        # Test proxy connection with configurable timeout
        proxy_timeout = config.get('proxy_timeout', 30)
        print(f"Testing HTTP proxy connection with {proxy_timeout}s timeout...")
        if not proxy_manager.test_proxy_connection(timeout=proxy_timeout):
            print("⚠️ HTTP proxy connection test failed, but continuing execution")
        else:
            print("✅ HTTP proxy connection test successful")
    else:
        print("⚠️ No proxy configured, may not be able to access Gemini API")

    return config

def create_openrouter_config():
    """Create OpenRouter Deepseek model configuration"""
    print(f"\n{'='*50}")
    print("Configuring OpenRouter model (clearing proxy)")
    print(f"{'='*50}")
    
    # Clear proxy settings (OpenRouter doesn't need proxy)
    proxy_manager.clear_proxy()

    config = {
        "use_llm": os.getenv('USE_LLM', 'true').lower() == 'true',
        "llm_service": os.getenv('MARKER_OPENROUTER_SERIVES'),
        "openai_api_key": os.getenv('OPENROUTER_API_KEY'),
        "openai_base_url": os.getenv('OPENROUTER_URL'),
        "openai_model": os.getenv('OPENROUTER_MODEL'),
        "format_lines": os.getenv('MARKER_FORMAT_LINES', 'true').lower() == 'true',
        "output_format": os.getenv('MARKER_OUTPUT_FORMAT', 'markdown'),
        "force_ocr": os.getenv('MARKER_FORCE_OCR', 'false').lower() == 'true',
        "batch_multiplier": int(os.getenv('MARKER_BATCH_MULTIPLIER', '1')),
        "langs": os.getenv('MARKER_LANG', 'en').split(',')
    }
    return config

def save_model_output(text, images, model_name, config, timestamp, base_dir="ocr_http_proxy_results"):
    """Save model output to separate folder, including markdown files and images"""
    # Create model-specific folder
    model_folder = Path(base_dir) / f"{model_name.replace(' ', '_').replace('/', '_')}_{timestamp}"
    model_folder.mkdir(parents=True, exist_ok=True)

    # Save markdown file
    markdown_file = model_folder / "ocr_result.md"
    with open(markdown_file, 'w', encoding='utf-8') as f:
        f.write(f"# {model_name} OCR Results (HTTP Proxy Test)\n\n")
        f.write(f"Generated at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        f.write("**Proxy Type**: HTTP (converted from SOCKS5 using pproxy)\n\n")

        # Add configuration information
        f.write("## Configuration Parameters\n\n")
        f.write(f"- **Batch Multiplier**: {config.get('batch_multiplier', 1)}\n")
        f.write(f"- **Languages**: {', '.join(config.get('langs', ['en']))}\n")
        f.write(f"- **Use LLM**: {config.get('use_llm', True)}\n")
        f.write(f"- **Force OCR**: {config.get('force_ocr', False)}\n")
        f.write(f"- **Format Lines**: {config.get('format_lines', True)}\n\n")

        f.write("## Extracted Text Content\n\n")
        f.write(text)

    # Save image files
    saved_images = []
    for i, (image_name, image_data) in enumerate(images.items()):
        # Use original image name if available, otherwise use index
        if image_name:
            image_filename = f"{image_name}"
        else:
            image_filename = f"image_{i+1}.png"

        image_path = model_folder / image_filename

        # Save image
        try:
            image_data.save(image_path)
            saved_images.append(image_filename)
            print(f"  Saved image: {image_filename}")
        except Exception as e:
            print(f"  Failed to save image {image_filename}: {e}")

    # Create image list file
    if saved_images:
        images_list_file = model_folder / "images_list.md"
        with open(images_list_file, 'w', encoding='utf-8') as f:
            f.write(f"# {model_name} Extracted Images List (HTTP Proxy Test)\n\n")
            f.write(f"Total extracted images: {len(saved_images)}\n\n")
            for img_name in saved_images:
                f.write(f"- ![{img_name}]({img_name})\n")

    print(f"  Model output saved to: {model_folder}")
    return model_folder

def test_model_with_http_proxy_retry(config, model_name, pdf_path="sample.pdf", max_retries=None):
    """Test specified model's OCR performance with HTTP proxy and retry mechanism"""
    # Use retry count from config if not specified, otherwise use config default
    if max_retries is None:
        max_retries = config.get('max_retries', 5)
    
    print(f"\n{'='*50}")
    print(f"Starting HTTP proxy test for {model_name}")
    print(f"Using extended timeout settings:")
    print(f"  API Timeout: {config.get('api_timeout', 600)} seconds")
    print(f"  Connection Timeout: {config.get('connection_timeout', 120)} seconds")
    print(f"  Proxy Timeout: {config.get('proxy_timeout', 60)} seconds")
    print(f"  Max Retries: {max_retries}")
    print(f"{'='*50}")
    
    # Create proxy check function
    check_proxy = ensure_proxy_persistence()
    error_message = "Unknown error occurred"

    for attempt in range(max_retries):
        try:
            if attempt > 0:
                print(f"\n🔄 Retry attempt {attempt + 1}/{max_retries}...")
                
                # Incremental wait time
                wait_time = attempt * 10  # 10s, 20s, 30s
                print(f"Waiting {wait_time} seconds before retry...")
                time.sleep(wait_time)
                
                # Re-check and set proxy
                if check_proxy():
                    print("✅ Proxy has been reset")
                
                # If Gemini model, ensure HTTP proxy is correctly set
                if "Gemini" in model_name:
                    nordvpn_proxy = os.getenv('NORDVPN_PROXY')
                    if nordvpn_proxy:
                        proxy_manager.set_global_proxy_with_conversion(nordvpn_proxy, force=True)
                        print("✅ Forced Gemini HTTP proxy reset")
                        
                        # Re-test proxy connection
                        proxy_timeout = config.get('proxy_timeout', 60)
                        print(f"Re-testing proxy connection with {proxy_timeout}s timeout...")
                        if proxy_manager.test_proxy_connection(timeout=proxy_timeout):
                            print("✅ Proxy connection restored")
                        else:
                            print("⚠️ Proxy connection still failing")
            
            # Set environment variables to extend timeout
            original_timeout = os.environ.get('PYTHONHTTPSVERIFY', '1')
            os.environ['PYTHONHTTPSVERIFY'] = '1'
            
            # If there are other timeout-related environment variables, set them too
            os.environ['REQUESTS_TIMEOUT'] = str(config.get('api_timeout', 300))
            
            # Create configuration parser and converter
            config_parser = ConfigParser(config)
            converter = PdfConverter(
                config=config_parser.generate_config_dict(),
                artifact_dict=create_model_dict(),
                processor_list=config_parser.get_processors(),
                renderer=config_parser.get_renderer(),
                llm_service=config_parser.get_llm_service()
            )

            # Record start time
            start_time = time.time()
            print(f"⏰ Start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"📄 Processing PDF: {pdf_path}")

            # Execute OCR conversion with timeout handling
            try:
                rendered = converter(pdf_path)
                text, _, images = text_from_rendered(rendered)
            except Exception as inner_e:
                # Check if it's a timeout error
                error_str = str(inner_e).lower()
                if any(timeout_keyword in error_str for timeout_keyword in 
                      ['timeout', 'timed out', 'time out', 'connection', 'read operation']):
                    print(f"⏰ Detected timeout error: {inner_e}")
                    if attempt < max_retries - 1:
                        print(f"Will retry with longer timeout...")
                        continue
                raise inner_e

            # Record end time
            end_time = time.time()
            processing_time = end_time - start_time

            print(f"✅ End time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"⏱️ Processing time: {processing_time:.2f} seconds")
            print(f"📝 Text length: {len(text)} characters")
            print(f"🖼️ Image count: {len(images)}")

            # Save model output to separate folder
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_folder = save_model_output(text, images, model_name, config, timestamp)

            return {
                'model_name': model_name,
                'processing_time': processing_time,
                'text_length': len(text),
                'image_count': len(images),
                'text_content': text,
                'images': images,
                'output_folder': output_folder,
                'config': config,
                'success': True,
                'error': None,
                'attempts': attempt + 1,
                'proxy_type': 'HTTP (converted from SOCKS5)'
            }

        except Exception as e:
            error_message = str(e)
            print(f"❌ Attempt {attempt + 1} failed: {error_message}")
            
            # Check error type and decide whether to retry
            error_str = error_message.lower()
            
            # Geolocation restriction error
            if "failed_precondition" in error_str and "location is not supported" in error_str:
                print("🌍 Detected geolocation restriction error")
                if attempt < max_retries - 1:
                    print("This may be a proxy configuration issue, will retry...")
                    continue
            
            # Timeout error
            elif any(timeout_keyword in error_str for timeout_keyword in 
                    ['timeout', 'timed out', 'time out', 'connection', 'read operation']):
                print("⏰ Detected timeout error")
                if attempt < max_retries - 1:
                    print("Will retry with extended timeout...")
                    # Dynamically increase timeout
                    if 'api_timeout' in config:
                        config['api_timeout'] = min(config['api_timeout'] * 1.5, 900)  # Maximum 15 minutes
                        print(f"Increased API timeout to {config['api_timeout']} seconds")
                    continue
                    
            # Network connection error
            elif any(network_keyword in error_str for network_keyword in 
                    ['network', 'connection', 'unreachable', 'refused']):
                print("🌐 Detected network connection error")
                if attempt < max_retries - 1:
                    print("Will retry with fresh proxy connection...")
                    continue
                    
            # Other errors, fail immediately
            else:
                print("💥 Non-retryable error detected, stopping retries")
                break

    # All retries failed
    print(f"\n❌ All {max_retries} attempts failed")
    return {
        'model_name': model_name,
        'processing_time': 0,
        'text_length': 0,
        'image_count': 0,
        'text_content': '',
        'images': {},
        'output_folder': None,
        'config': config,
        'success': False,
        'error': error_message,
        'attempts': max_retries,
        'proxy_type': 'HTTP (converted from SOCKS5)'
    }

def test_model_http_proxy(config, model_name, pdf_path="sample.pdf"):
    """Test specified model's OCR performance with HTTP proxy (maintain interface compatibility)"""
    return test_model_with_http_proxy_retry(config, model_name, pdf_path)

def test_model(config, model_name, pdf_path="sample.pdf"):
    """Test specified model's OCR performance (alias for compatibility with main.py)"""
    return test_model_with_http_proxy_retry(config, model_name, pdf_path)

def save_results_to_markdown(results, output_dir="ocr_comparison_results"):
    """Save test results to Markdown file"""
    # Create output directory
    Path(output_dir).mkdir(exist_ok=True)

    # Generate timestamp
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

    # Create comparison report
    comparison_file = Path(output_dir) / f"ocr_comparison_summary_{timestamp}.md"

    with open(comparison_file, 'w', encoding='utf-8') as f:
        f.write("# OCR Model Comparison Test Report\n\n")
        f.write(f"Test time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

        # Performance comparison table
        f.write("## Performance Comparison\n\n")
        f.write("| Model | Processing Time(s) | Text Length(chars) | Image Count | Batch Multiplier | Languages | Retry Count | Output Folder | Status |\n")
        f.write("|-------|-------------------|-------------------|-------------|-----------------|-----------|-------------|---------------|--------|\n")

        for result in results:
            status = "✅ Success" if result['success'] else "❌ Failed"
            folder_name = result['output_folder'].name if result['output_folder'] else "N/A"
            batch_multiplier = result.get('config', {}).get('batch_multiplier', 1)
            langs = ', '.join(result.get('config', {}).get('langs', ['en']))
            attempts = result.get('attempts', 1)
            f.write(f"| {result['model_name']} | {result['processing_time']:.2f} | {result['text_length']} | {result['image_count']} | {batch_multiplier} | {langs} | {attempts} | `{folder_name}` | {status} |\n")

        f.write("\n")

        # Folder structure description
        f.write("## Output Folder Structure\n\n")
        f.write("Each model's results are saved in separate folders containing the following files:\n\n")
        f.write("```\n")
        f.write("ModelName_Timestamp/\n")
        f.write("├── ocr_result.md      # Complete OCR extracted text content\n")
        f.write("├── images_list.md     # List of extracted images\n")
        f.write("├── image_1.png        # Extracted image files\n")
        f.write("├── image_2.png        # ...\n")
        f.write("└── ...\n")
        f.write("```\n\n")

        # Detailed result summary
        for result in results:
            f.write(f"## {result['model_name']} Result Summary\n\n")

            if result['success']:
                f.write(f"- **Processing Time**: {result['processing_time']:.2f} seconds\n")
                f.write(f"- **Text Length**: {result['text_length']} characters\n")
                f.write(f"- **Image Count**: {result['image_count']}\n")
                f.write(f"- **Retry Count**: {result.get('attempts', 1)}\n")
                f.write(f"- **Output Folder**: `{result['output_folder'].name}`\n\n")
                f.write("### Image Segmentation Results\n\n")
                if result['images']:
                    for img_name in result['images'].keys():
                        f.write(f"- {img_name}\n")
                else:
                    f.write("- No images extracted\n")
                f.write(f"\n**Full content see**: `{result['output_folder']}/ocr_result.md`\n\n")
            else:
                f.write(f"- **Error**: {result['error']}\n")
                f.write(f"- **Retry Count**: {result.get('attempts', 1)}\n\n")

    print(f"\nComparison summary report saved to: {comparison_file}")
    return comparison_file

if __name__ == "__main__":
    print("=== HTTP Proxy OCR Test ===")
    print("This test uses pproxy to convert SOCKS5 proxy to HTTP proxy")
    print("Testing Gemini API compatibility with HTTP proxy vs SOCKS5 proxy")
    print("="*50)
    
    # Test Gemini with HTTP proxy
    gemini_config = create_gemini_config_with_http_proxy()
    gemini_result = test_model_http_proxy(gemini_config, "Gemini 2.5 Flash (HTTP Proxy)")
    
    if gemini_result['success']:
        print(f"\n✅ Gemini HTTP proxy test successful!")
        print(f"Processing time: {gemini_result['processing_time']:.2f}s")
        print(f"Output folder: {gemini_result['output_folder']}")
    else:
        print(f"\n❌ Gemini HTTP proxy test failed: {gemini_result['error']}")
    
    # Clean up proxy converter
    proxy_manager.clear_proxy()
    print("\n=== HTTP Proxy Test Complete ===")
