# markerpdf Package

A comprehensive testing package for marker-pdf OCR functionality with proxy support, designed to compare the performance of different proxy types (SOCKS5 vs HTTP) when processing PDF documents using the marker-pdf library.

## Overview

This package provides tools to test and compare the performance of marker-pdf OCR processing under different proxy configurations:
- **SOCKS5 Proxy**: Direct SOCKS5 proxy connection
- **HTTP Proxy**: SOCKS5-to-HTTP proxy conversion using pproxy

## Test Results Summary

Based on recent testing with Gemini 2.5 Flash:

| Proxy Type | Processing Time | Setup Time | Total Time | Performance |
|------------|----------------|------------|------------|-------------|
| **SOCKS5** | 177.05s | 0.34s | 177.39s | Baseline |
| **HTTP** | 79.36s | 5.53s | 84.89s | **🏆 123% faster** |

**Key Findings:**
- HTTP proxy (via SOCKS5 conversion) is **97.69 seconds faster** in processing
- Despite 5.19s additional setup overhead, HTTP proxy is **52% faster overall**
- Both proxy types successfully process the same content (4005 characters extracted)

## Package Structure

```
markerpdf/
├── __init__.py                    # Package initialization and exports
├── README.md                      # This documentation
├── proxy_manager.py               # Proxy management and conversion utilities
├── marker_ocr_socks5_proxy.py     # SOCKS5 proxy testing functions
├── marker_ocr_http_proxy.py       # HTTP proxy testing functions
└── compare_proxy_types.py         # Performance comparison framework
```

## Quick Start

### Installation Dependencies

```bash
# Install required SOCKS support
uv add pysocks "requests[socks]"

# Or with pip
pip install pysocks requests[socks]
```

### Basic Usage

```python
# Import the package
from markerpdf import (
    create_gemini_config,
    create_gemini_config_with_http_proxy,
    test_model,
    test_model_http_proxy,
    compare_main
)

# Test SOCKS5 proxy
socks5_config = create_gemini_config()
socks5_result = test_model(socks5_config, "Gemini 2.5 Flash (SOCKS5)")

# Test HTTP proxy (converted from SOCKS5)
http_config = create_gemini_config_with_http_proxy()
http_result = test_model_http_proxy(http_config, "Gemini 2.5 Flash (HTTP)")

# Run full comparison
compare_main()
```

### Command Line Testing

```bash
# Test individual proxy types
python test_individual_proxy.py socks5    # Test SOCKS5 only
python test_individual_proxy.py http      # Test HTTP only  
python test_individual_proxy.py both      # Test both types

# Run full performance comparison
python test_proxy_comparison.py
```

## Environment Variables

Required environment variables in your `.env` file:

```bash
# Proxy Configuration
NORDVPN_PROXY=socks5://username:password@server:port

# Gemini API Configuration
GEMINI_API_KEY=your_gemini_api_key
GEMINI_MODEL=gemini-2.5-flash
MARKER_GEMINI_SERIVES=gemini

# Optional Timeout Settings
GEMINI_API_TIMEOUT=600          # 10 minutes
CONNECTION_TIMEOUT=120          # 2 minutes  
PROXY_TIMEOUT=60               # 1 minute
MAX_RETRIES=5                  # Maximum retry attempts
```

## Core Components

### ProxyManager

Handles proxy configuration, conversion, and management:

```python
from markerpdf import ProxyManager

proxy_manager = ProxyManager()

# Set SOCKS5 proxy
proxy_manager.set_global_proxy("socks5://user:pass@host:port")

# Set with automatic SOCKS5→HTTP conversion
proxy_manager.set_global_proxy_with_conversion("socks5://user:pass@host:port")

# Test proxy connection
proxy_manager.test_proxy_connection()

# Clear all proxy settings
proxy_manager.clear_proxy()
```

### Performance Testing

The package includes sophisticated timing mechanisms that separate:
1. **Setup Time**: Proxy configuration and connection establishment
2. **Processing Time**: Pure PDF processing (excluding proxy setup)
3. **Total Time**: Complete end-to-end timing

This ensures fair performance comparisons between proxy types.

### SOCKS5 to HTTP Conversion

The package automatically converts SOCKS5 proxies to HTTP using pproxy:

```python
# Automatic conversion happens behind the scenes
config = create_gemini_config_with_http_proxy()
# SOCKS5 proxy is converted to local HTTP proxy at 127.0.0.1:8888
```

## Output and Reports

### Individual Test Results

Each test generates:
- Markdown file with extracted text content
- Performance metrics (processing time, text length, etc.)
- Configuration parameters used
- Error handling and retry information

### Comparison Reports

Full comparison generates comprehensive reports in `proxy_comparison_results/`:

```
proxy_comparison_YYYYMMDD_HHMMSS.md
├── Performance comparison table
├── Detailed timing analysis  
├── Technical implementation details
└── Conclusions and recommendations
```

## API Reference

### Configuration Functions

- `create_gemini_config()` - Configure Gemini with SOCKS5 proxy
- `create_gemini_config_with_http_proxy()` - Configure Gemini with HTTP proxy
- `create_openrouter_config()` - Configure OpenRouter (no proxy)

### Testing Functions

- `test_model(config, model_name, pdf_path)` - Test with SOCKS5 proxy
- `test_model_http_proxy(config, model_name, pdf_path)` - Test with HTTP proxy
- `test_socks5_proxy_with_timing()` - Detailed SOCKS5 performance test
- `test_http_proxy_with_timing()` - Detailed HTTP performance test

### Comparison Functions

- `compare_main()` - Run full proxy comparison
- `save_comparison_results()` - Generate comparison report

### Utility Functions

- `setup_early_proxy()` - Set proxy before library imports
- `ensure_proxy_persistence()` - Monitor and maintain proxy settings
- `save_model_output()` - Save test results to files

## Performance Optimization

### HTTP Proxy Advantages

Based on testing, HTTP proxy shows significant performance benefits:

1. **Faster Processing**: 123% faster than SOCKS5
2. **Better Compatibility**: More reliable with various network libraries
3. **Enhanced Debugging**: Easier to monitor and troubleshoot

### Recommendations

- **For Production**: Use HTTP proxy conversion for better performance
- **For Development**: SOCKS5 direct connection for simpler setup
- **For CI/CD**: HTTP proxy for more reliable automated testing

## Troubleshooting

### Common Issues

1. **Missing SOCKS Dependencies**
   ```bash
   # Solution: Install SOCKS support
   uv add pysocks "requests[socks]"
   ```

2. **Proxy Connection Failures**
   ```python
   # Check proxy settings
   proxy_manager.verify_proxy_settings()
   proxy_manager.test_proxy_connection()
   ```

3. **pproxy Converter Issues**
   ```bash
   # Ensure pproxy is installed
   uv add pproxy
   ```

### Debug Mode

Enable verbose logging by setting environment variables:
```bash
export PYTHONPATH=.
export DEBUG_PROXY=1
```

## Contributing

When adding new functionality:

1. Update the `__init__.py` exports
2. Add appropriate error handling and retry logic
3. Include timing measurements for performance analysis
4. Update this README with new features

## License

This package is part of the OCR comparison project and follows the same licensing terms. 